# Tomcat
server:
  port: 9200

# Spring
spring:
  application:
    # 应用名称
    name: node-auth
  profiles:
    # 环境配置
    active: @profiles.active@
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: @nacos.server@
        namespace: @nacos.namespace@
      config:
        # 配置中心地址
        server-addr: @nacos.server@
        namespace: @nacos.namespace@
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

# OAuth2 Configuration
#oauth2:
#  client-id: Ov23liSEGaNZxxBWavgJ
#  client-secret: e62b55b7ced4addac66167cdbe2e6e326215d76c
#  redirect-uri: http://local.biosino.org:9200/login/oauth2
#  authorization-uri: https://github.com/login/oauth/authorize
#  token-uri: https://github.com/login/oauth/access_token
#  user-info-uri: https://api.github.com/user
#  scope: user:email

oauth2:
  client-id: 0oat4cwxrrKBiXzfs697
  client-secret: e1OrcgqZVYRuzwpNDQL5kvmuigDIAJE70YyZfdfDEZb-f4jKOk7Olpjy_C28N122
  redirect-uri: http://local.biosino.org:9200/login/oauth2
  authorization-uri: https://trial-9739209.okta.com/oauth2/default/v1/authorize
  token-uri: https://trial-9739209.okta.com/oauth2/default/v1/token
  token-revoke-uri: https://trial-9739209.okta.com/oauth2/default/v1/token
  user-info-uri: https://trial-9739209.okta.com/oauth2/default/v1/userinfo
  scope: openid profile email

# Application URLs
app:
  client:
    url: http://local.biosino.org  # 前端应用地址
  server:
    url: http://local.biosino.org:9200  # 后端应用地址
