package org.biosino.auth.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.auth.oauth2.OAuth2Properties;
import org.biosino.auth.oauth2.model.OAuthUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * OAuth2远程服务调用
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OAuth2RemoteService {

    @Autowired
    private OAuth2Properties oAuth2Properties;

    /**
     * 使用授权码获取Okta access token
     *
     * @param code 授权码
     * @return access token
     */
    public String getOktaAccessToken(String code) {
        try {
            // 准备POST请求的表单数据
            Map<String, Object> formData = new HashMap<>();
            formData.put("grant_type", "authorization_code");
            formData.put("code", code);
            formData.put("redirect_uri", oAuth2Properties.getRedirectUri());

            // 使用 HttpRequest 构建请求
            HttpResponse response = HttpRequest.post(oAuth2Properties.getTokenUri())
                    .basicAuth(oAuth2Properties.getClientId(), oAuth2Properties.getClientSecret())
                    .form(formData)
                    .timeout(20000)
                    .execute();

            String body = response.body();
            JSONObject entries = JSONUtil.parseObj(body);
            String accessToken = entries.getStr("access_token");

            log.info("成功获取Okta access token");
            return accessToken;

        } catch (Exception e) {
            log.error("获取Okta access token失败", e);
            throw new RuntimeException("获取OAuth2 access token失败: " + e.getMessage());
        }
    }

    /**
     * 使用access token获取Okta用户信息
     *
     * @param accessToken access token
     * @return 用户信息
     */
    public OAuthUser getOktaUserInfo(String accessToken) {
        try {
            HttpResponse response = HttpRequest.get(oAuth2Properties.getUserInfoUri())
                    .header("Accept", "application/json")
                    .bearerAuth(accessToken)
                    .timeout(20000)
                    .execute();

            String body = response.body();
            OAuthUser result = JSONUtil.toBean(body, OAuthUser.class);
            result.setId(JSONUtil.parseObj(body).getStr("sub"));

            log.info("成功获取Okta用户信息: {}", result.getEmail());
            return result;

        } catch (Exception e) {
            log.error("获取Okta用户信息失败", e);
            throw new RuntimeException("获取OAuth2用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 撤销OAuth2 access token (Okta)
     *
     * @param accessToken 要撤销的access token
     */
    public void revokeOAuth2AccessToken(String accessToken) {
        try {
            // Okta的token撤销端点
            String revokeUrl = oAuth2Properties.getTokenRevokeUri();

            // 准备POST请求的表单数据
            Map<String, Object> formData = new HashMap<>();
            formData.put("token", accessToken);
            formData.put("token_type_hint", "access_token");

            // 使用 HttpRequest 构建请求
            HttpResponse response = HttpRequest.post(revokeUrl)
                    .basicAuth(oAuth2Properties.getClientId(), oAuth2Properties.getClientSecret())
                    .form(formData)
                    .timeout(20000)
                    .execute();

            if (response.getStatus() == 200) {
                log.info("OAuth2 access token撤销成功");
            } else {
                log.warn("OAuth2 access token撤销返回状态码: {}", response.getStatus());
            }

        } catch (Exception e) {
            log.warn("撤销OAuth2 access token失败，但不影响本地登出: {}", e.getMessage());
            throw new RuntimeException("撤销OAuth2 access token失败: " + e.getMessage());
        }
    }
}
